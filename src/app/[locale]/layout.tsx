import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import { Inter, Noto_Sans_SC } from 'next/font/google';
import '../globals.css';

// 英文字体
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

// 中文字体
const notoSansSC = Noto_Sans_SC({
  subsets: ['latin'],
  variable: '--font-noto-sans-sc',
  display: 'swap',
});

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
  params: { locale }
}: {
  params: { locale: string };
}) {
  const messages = await getMessages();
  const meta = messages.meta as any;

  return {
    title: meta.title,
    description: meta.description,
    keywords: meta.keywords,
    openGraph: {
      title: meta.title,
      description: meta.description,
      url: `https://noisesleep.com${locale === 'en' ? '' : '/zh'}`,
      siteName: 'NoiseSleep',
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: meta.title,
      description: meta.description,
    },
    alternates: {
      canonical: `https://noisesleep.com${locale === 'en' ? '' : '/zh'}`,
      languages: {
        'en': 'https://noisesleep.com',
        'zh': 'https://noisesleep.com/zh',
      },
    },
  };
}

export default async function LocaleLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  // 验证locale是否有效
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }

  // 获取翻译消息
  const messages = await getMessages();

  return (
    <html 
      lang={locale} 
      dir="ltr" 
      className={`${inter.variable} ${notoSansSC.variable}`}
    >
      <head>
        {/* 多语言hreflang标签 */}
        <link rel="alternate" hrefLang="en" href="https://noisesleep.com" />
        <link rel="alternate" hrefLang="zh" href="https://noisesleep.com/zh" />
        <link rel="alternate" hrefLang="x-default" href="https://noisesleep.com" />
        
        {/* PWA配置 */}
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#f59e0b" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        
        {/* 性能优化 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link rel="dns-prefetch" href="https://cdn.noisesleep.com" />
        
        {/* 安全策略 */}
        <meta httpEquiv="Content-Security-Policy" content="
          default-src 'self';
          script-src 'self' 'unsafe-inline' https://www.googletagmanager.com;
          style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
          font-src 'self' https://fonts.gstatic.com;
          img-src 'self' data: https:;
          media-src 'self' blob: https://cdn.noisesleep.com;
          connect-src 'self' https://www.google-analytics.com;
        " />
      </head>
      <body 
        className={`
          ${locale === 'zh' ? 'font-noto-sans-sc' : 'font-inter'}
          antialiased
          bg-white dark:bg-gray-900
          text-gray-900 dark:text-gray-100
          transition-colors duration-300
        `}
        data-locale={locale}
      >
        <NextIntlClientProvider messages={messages}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
