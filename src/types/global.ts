// 全局环境变量类型
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test';
      NEXT_PUBLIC_SITE_URL: string;
      NEXT_PUBLIC_GA_ID?: string;
      NEXT_PUBLIC_CDN_URL?: string;
      CLOUDFLARE_API_TOKEN?: string;
      CLOUDFLARE_ACCOUNT_ID?: string;
    }
  }

  // Web Audio API扩展
  interface Window {
    webkitAudioContext?: typeof AudioContext;
    gtag?: (...args: any[]) => void;
  }

  // AudioParam扩展（为Firefox兼容性）
  interface AudioParam {
    cancelAndHoldAtTime?: (when: number) => AudioParam;
  }
}

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    version: string;
    requestId: string;
  };
}

// 分页响应类型
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  type: 'network' | 'audio' | 'validation' | 'permission' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  context?: Record<string, any>;
  stack?: string;
}

// 设备信息类型
export interface DeviceInfo {
  type: 'mobile' | 'tablet' | 'desktop';
  os: string;
  browser: string;
  version: string;
  screenSize: {
    width: number;
    height: number;
  };
  pixelRatio: number;
  touchSupport: boolean;
  audioSupport: {
    webAudio: boolean;
    audioWorklet: boolean;
    mediaStreamTrack: boolean;
    outputLatency: boolean;
  };
}

// 性能指标类型
export interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte

  // 自定义指标
  audioLoadTime: number;
  pageLoadTime: number;
  memoryUsage: number;
  
  // 时间戳
  timestamp: string;
  url: string;
  userAgent: string;
}

// 分析事件类型
export interface AnalyticsEvent {
  name: string;
  category: 'audio' | 'navigation' | 'user_interaction' | 'performance' | 'error';
  action: string;
  label?: string;
  value?: number;
  customParameters?: Record<string, any>;
  timestamp: string;
  sessionId: string;
  userId?: string;
}

// 应用配置类型
export interface AppConfig {
  // 基础配置
  name: string;
  version: string;
  environment: 'development' | 'staging' | 'production';
  
  // API配置
  apiUrl: string;
  cdnUrl: string;
  
  // 功能开关
  features: {
    analytics: boolean;
    errorReporting: boolean;
    performanceMonitoring: boolean;
    betaFeatures: boolean;
    offlineMode: boolean;
  };
  
  // 限制配置
  limits: {
    maxSimultaneousAudio: number;
    maxFavorites: number;
    maxPlaylistLength: number;
    maxFileSize: number; // bytes
  };
  
  // 音频配置
  audio: {
    defaultVolume: number;
    fadeInDuration: number;
    fadeOutDuration: number;
    preloadStrategy: 'none' | 'metadata' | 'auto';
    supportedFormats: string[];
  };
  
  // 缓存配置
  cache: {
    audioTTL: number; // seconds
    metadataTTL: number; // seconds
    maxCacheSize: number; // bytes
  };
}

// 主题类型
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: {
      primary: string;
      secondary: string;
      disabled: string;
    };
    border: string;
    error: string;
    warning: string;
    success: string;
    info: string;
  };
  
  typography: {
    fontFamily: {
      primary: string;
      secondary: string;
      mono: string;
    };
    fontSize: {
      xs: string;
      sm: string;
      base: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
      '4xl': string;
    };
    fontWeight: {
      normal: number;
      medium: number;
      semibold: number;
      bold: number;
    };
    lineHeight: {
      tight: number;
      normal: number;
      relaxed: number;
    };
  };
  
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
  };
  
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    full: string;
  };
  
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  
  animation: {
    duration: {
      fast: string;
      normal: string;
      slow: string;
    };
    easing: {
      linear: string;
      easeIn: string;
      easeOut: string;
      easeInOut: string;
    };
  };
}

// 通用工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// 状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';
export type AsyncState<T> = {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: string | null;
};

// 导出类型
export type GlobalTypes = 
  | ApiResponse
  | PaginatedResponse<any>
  | AppError
  | DeviceInfo
  | PerformanceMetrics
  | AnalyticsEvent
  | AppConfig
  | Theme;
