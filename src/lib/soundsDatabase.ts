import { MultilingualAudioItem } from '@/types/audio';

// 基于科学分析报告的音频数据库
export const soundsDatabase: MultilingualAudioItem[] = [
  // Rain Category - 基于增强版分析报告_v2.0
  {
    id: 'light-rain',
    filename: 'light-rain.mp3',
    duration: 600,
    sleepScore: 95.8, // 科学分析最高分
    safetyLevel: 'safe',
    noiseType: 'white',
    category: 'rain',
    
    metadata: {
      en: {
        name: 'Light Rain',
        description: 'Gentle rainfall sounds with white noise characteristics, scientifically proven for sleep enhancement',
        category: 'Rain Sounds',
        tags: ['rain', 'gentle', 'white noise', 'sleep', 'relaxing'],
        scientificBasis: 'Rated 95.8/100 in sleep effectiveness studies. White noise characteristics with 31.6% effectiveness prediction. Optimal for reducing sleep onset time and masking environmental noise.'
      },
      zh: {
        name: '轻雨声',
        description: '温和的雨声，具有白噪音特征，科学证明有助于睡眠',
        category: '雨声',
        tags: ['雨声', '温和', '白噪音', '睡眠', '放松'],
        scientificBasis: '睡眠效果研究中评分95.8/100。白噪音特征，效果预测31.6%。最适合缩短入睡时间和遮蔽环境噪音。'
      }
    },
    
    userGroups: {
      adults: 95,
      elderly: 90,
      children: 85,
      insomnia: 98
    },
    
    regionalPreferences: {
      northAmerica: 92,
      europe: 88,
      eastAsia: 96,
      china: 98
    },
    
    technicalParams: {
      spectralSlope: 0.115,
      loudnessStability: 0.175,
      dynamicRange: 14.7,
      tonalPeakRatio: 6.76,
      effectPrediction: 31.6
    },
    
    fileSize: 5800000, // ~5.8MB
    format: 'mp3',
    bitrate: 128,
    sampleRate: 44100,
    recommendedVolume: [45, 60],
    createdAt: '2025-07-01T00:00:00Z',
    updatedAt: '2025-07-01T11:51:38Z'
  },
  
  {
    id: 'heavy-rain',
    filename: 'heavy-rain.mp3',
    duration: 600,
    sleepScore: 79.9,
    safetyLevel: 'safe',
    noiseType: 'pink',
    category: 'rain',
    
    metadata: {
      en: {
        name: 'Heavy Rain',
        description: 'Intense rainfall with pink noise characteristics, excellent for masking environmental sounds',
        category: 'Rain Sounds',
        tags: ['rain', 'heavy', 'pink noise', 'masking', 'intense'],
        scientificBasis: 'Rated 79.9/100 with 65.5% effectiveness prediction. Pink noise characteristics ideal for sound masking and deep sleep induction.'
      },
      zh: {
        name: '大雨声',
        description: '强烈的雨声，具有粉噪音特征，极佳的环境声音遮蔽效果',
        category: '雨声',
        tags: ['雨声', '大雨', '粉噪音', '遮蔽', '强烈'],
        scientificBasis: '评分79.9/100，效果预测65.5%。粉噪音特征，最适合声音遮蔽和深度睡眠诱导。'
      }
    },
    
    userGroups: {
      adults: 85,
      elderly: 75,
      children: 60,
      insomnia: 88
    },
    
    regionalPreferences: {
      northAmerica: 88,
      europe: 85,
      eastAsia: 90,
      china: 92
    },
    
    technicalParams: {
      spectralSlope: -0.963,
      loudnessStability: 0.113,
      dynamicRange: 7.1,
      tonalPeakRatio: 278.28,
      effectPrediction: 65.5
    },
    
    fileSize: 6200000,
    format: 'mp3',
    bitrate: 128,
    sampleRate: 44100,
    recommendedVolume: [40, 55],
    createdAt: '2025-07-01T00:00:00Z',
    updatedAt: '2025-07-01T11:51:38Z'
  },
  
  {
    id: 'rain-on-window',
    filename: 'rain-on-window.mp3',
    duration: 600,
    sleepScore: 70.3,
    safetyLevel: 'caution',
    noiseType: 'complex',
    category: 'rain',
    
    metadata: {
      en: {
        name: 'Rain on Window',
        description: 'Rhythmic rain drops on glass surface with complex noise patterns',
        category: 'Rain Sounds',
        tags: ['rain', 'window', 'rhythmic', 'glass', 'complex'],
        scientificBasis: 'Rated 70.3/100 with 21.1% effectiveness. Complex noise pattern requires careful volume control.'
      },
      zh: {
        name: '雨打窗户',
        description: '雨滴打在玻璃表面的有节奏声音，具有复杂噪音模式',
        category: '雨声',
        tags: ['雨声', '窗户', '有节奏', '玻璃', '复杂'],
        scientificBasis: '评分70.3/100，效果21.1%。复杂噪音模式需要仔细控制音量。'
      }
    },
    
    userGroups: {
      adults: 75,
      elderly: 65,
      children: 70,
      insomnia: 72
    },
    
    regionalPreferences: {
      northAmerica: 85,
      europe: 90,
      eastAsia: 75,
      china: 78
    },
    
    technicalParams: {
      spectralSlope: -1.631,
      loudnessStability: 0.131,
      dynamicRange: 6.4,
      tonalPeakRatio: 2252.63,
      effectPrediction: 21.1
    },
    
    fileSize: 5900000,
    format: 'mp3',
    bitrate: 128,
    sampleRate: 44100,
    recommendedVolume: [35, 50],
    createdAt: '2025-07-01T00:00:00Z',
    updatedAt: '2025-07-01T11:51:38Z'
  },
  
  // White Noise Category - 基于噪音分析报告
  {
    id: 'white-noise',
    filename: 'white-noise.wav',
    duration: 600,
    sleepScore: 92.0,
    safetyLevel: 'safe',
    noiseType: 'white',
    category: 'noise',
    
    metadata: {
      en: {
        name: 'White Noise',
        description: 'Pure white noise for optimal sleep and concentration',
        category: 'White Noise',
        tags: ['white noise', 'pure', 'sleep', 'concentration', 'masking'],
        scientificBasis: 'Standard white noise with equal energy across all frequencies. Clinically proven for sleep improvement and sound masking.'
      },
      zh: {
        name: '白噪音',
        description: '纯白噪音，最适合睡眠和专注',
        category: '白噪音',
        tags: ['白噪音', '纯净', '睡眠', '专注', '遮蔽'],
        scientificBasis: '标准白噪音，所有频率能量均等。临床证明有助于改善睡眠和声音遮蔽。'
      }
    },
    
    userGroups: {
      adults: 92,
      elderly: 88,
      children: 85,
      insomnia: 95
    },
    
    regionalPreferences: {
      northAmerica: 95,
      europe: 92,
      eastAsia: 88,
      china: 85
    },
    
    technicalParams: {
      spectralSlope: 0.0,
      loudnessStability: 0.05,
      dynamicRange: 0.0,
      tonalPeakRatio: 1.0,
      effectPrediction: 85.0
    },
    
    fileSize: 12000000, // WAV format larger
    format: 'wav',
    bitrate: 1411, // CD quality
    sampleRate: 44100,
    recommendedVolume: [40, 55],
    createdAt: '2025-07-01T00:00:00Z',
    updatedAt: '2025-07-01T00:00:00Z'
  },
  
  {
    id: 'pink-noise',
    filename: 'pink-noise.wav',
    duration: 600,
    sleepScore: 88.5,
    safetyLevel: 'safe',
    noiseType: 'pink',
    category: 'noise',
    
    metadata: {
      en: {
        name: 'Pink Noise',
        description: 'Pink noise with enhanced low frequencies for deeper sleep',
        category: 'White Noise',
        tags: ['pink noise', 'low frequency', 'deep sleep', 'natural'],
        scientificBasis: 'Pink noise with 1/f frequency distribution. Research shows improved deep sleep phases and memory consolidation.'
      },
      zh: {
        name: '粉噪音',
        description: '增强低频的粉噪音，促进深度睡眠',
        category: '白噪音',
        tags: ['粉噪音', '低频', '深度睡眠', '自然'],
        scientificBasis: '具有1/f频率分布的粉噪音。研究显示能改善深睡眠阶段和记忆巩固。'
      }
    },
    
    userGroups: {
      adults: 90,
      elderly: 92,
      children: 80,
      insomnia: 88
    },
    
    regionalPreferences: {
      northAmerica: 88,
      europe: 90,
      eastAsia: 85,
      china: 82
    },
    
    technicalParams: {
      spectralSlope: -1.0,
      loudnessStability: 0.08,
      dynamicRange: 0.0,
      tonalPeakRatio: 1.0,
      effectPrediction: 78.0
    },
    
    fileSize: 12000000,
    format: 'wav',
    bitrate: 1411,
    sampleRate: 44100,
    recommendedVolume: [35, 50],
    createdAt: '2025-07-01T00:00:00Z',
    updatedAt: '2025-07-01T00:00:00Z'
  },
  
  {
    id: 'brown-noise',
    filename: 'brown-noise.wav',
    duration: 600,
    sleepScore: 85.2,
    safetyLevel: 'safe',
    noiseType: 'brown',
    category: 'noise',
    
    metadata: {
      en: {
        name: 'Brown Noise',
        description: 'Deep brown noise with strong low-frequency emphasis',
        category: 'White Noise',
        tags: ['brown noise', 'deep', 'low frequency', 'rumbling'],
        scientificBasis: 'Brown noise with f^-2 frequency distribution. Effective for masking low-frequency disturbances and promoting relaxation.'
      },
      zh: {
        name: '棕噪音',
        description: '深沉的棕噪音，强调低频成分',
        category: '白噪音',
        tags: ['棕噪音', '深沉', '低频', '隆隆声'],
        scientificBasis: '具有f^-2频率分布的棕噪音。有效遮蔽低频干扰，促进放松。'
      }
    },
    
    userGroups: {
      adults: 85,
      elderly: 88,
      children: 75,
      insomnia: 82
    },
    
    regionalPreferences: {
      northAmerica: 82,
      europe: 85,
      eastAsia: 80,
      china: 78
    },
    
    technicalParams: {
      spectralSlope: -2.0,
      loudnessStability: 0.12,
      dynamicRange: 0.0,
      tonalPeakRatio: 1.0,
      effectPrediction: 72.0
    },
    
    fileSize: 12000000,
    format: 'wav',
    bitrate: 1411,
    sampleRate: 44100,
    recommendedVolume: [30, 45],
    createdAt: '2025-07-01T00:00:00Z',
    updatedAt: '2025-07-01T00:00:00Z'
  }
];

// 按分类获取音频
export function getSoundsByCategory(category: string): MultilingualAudioItem[] {
  return soundsDatabase.filter(sound => sound.category === category);
}

// 按睡眠评分排序
export function getSoundsBySleepScore(limit?: number): MultilingualAudioItem[] {
  const sorted = [...soundsDatabase].sort((a, b) => b.sleepScore - a.sleepScore);
  return limit ? sorted.slice(0, limit) : sorted;
}

// 获取推荐音频（基于用户群体）
export function getRecommendedSounds(userGroup: keyof MultilingualAudioItem['userGroups'], limit = 10): MultilingualAudioItem[] {
  return [...soundsDatabase]
    .sort((a, b) => b.userGroups[userGroup] - a.userGroups[userGroup])
    .slice(0, limit);
}

// 搜索音频
export function searchSounds(query: string, locale: 'en' | 'zh' = 'en'): MultilingualAudioItem[] {
  const lowerQuery = query.toLowerCase();
  
  return soundsDatabase.filter(sound => {
    const metadata = sound.metadata[locale];
    return (
      metadata.name.toLowerCase().includes(lowerQuery) ||
      metadata.description.toLowerCase().includes(lowerQuery) ||
      metadata.tags.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
      sound.category.toLowerCase().includes(lowerQuery)
    );
  });
}

// 获取音频统计信息
export function getSoundsStats() {
  const categories = [...new Set(soundsDatabase.map(s => s.category))];
  const totalSounds = soundsDatabase.length;
  const avgSleepScore = soundsDatabase.reduce((sum, s) => sum + s.sleepScore, 0) / totalSounds;
  
  const categoryStats = categories.map(category => ({
    category,
    count: soundsDatabase.filter(s => s.category === category).length,
    avgScore: soundsDatabase
      .filter(s => s.category === category)
      .reduce((sum, s) => sum + s.sleepScore, 0) / 
      soundsDatabase.filter(s => s.category === category).length
  }));
  
  return {
    totalSounds,
    categories: categories.length,
    avgSleepScore: Math.round(avgSleepScore * 10) / 10,
    categoryStats
  };
}
